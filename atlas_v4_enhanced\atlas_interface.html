<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Trading System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
        }

        /* Left Scanner Panel */
        .scanner-panel {
            width: 400px;
            background: #1a1a2e;
            border-right: 1px solid #16213e;
            display: flex;
            flex-direction: column;
        }

        .scanner-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .scanner-title {
            font-size: 18px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 8px;
        }

        .scanner-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .scanner-stats {
            display: flex;
            justify-content: space-between;
            padding: 15px 20px;
            background: #0f0f23;
            border-bottom: 1px solid #16213e;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: #00ff88;
        }

        .stat-label {
            font-size: 12px;
            color: #8892b0;
            margin-top: 4px;
        }

        .scanner-controls {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #00ff88;
            color: #0f0f23;
        }

        .btn-primary:hover {
            background: #00cc6a;
        }

        .btn-secondary {
            background: #16213e;
            color: #ffffff;
            border: 1px solid #8892b0;
        }

        .btn-secondary:hover {
            background: #1e2a4a;
        }

        .scanner-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .signal-item {
            padding: 15px 20px;
            border-bottom: 1px solid #16213e;
            cursor: pointer;
            transition: background 0.2s;
        }

        .signal-item:hover {
            background: #16213e;
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .signal-symbol {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .signal-price {
            font-size: 14px;
            color: #00ff88;
        }

        .signal-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .signal-confidence {
            font-size: 12px;
            color: #8892b0;
        }

        .signal-strength {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .strength-high {
            background: #00ff88;
            color: #0f0f23;
        }

        .strength-medium {
            background: #ffa500;
            color: #0f0f23;
        }

        .strength-low {
            background: #ff6b6b;
            color: #ffffff;
        }

        /* Right Chat Panel */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #0f0f23;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #16213e;
            background: #16213e;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .chat-subtitle {
            font-size: 14px;
            color: #8892b0;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message-user {
            align-self: flex-end;
            background: #00ff88;
            color: #0f0f23;
        }

        .message-bot {
            align-self: flex-start;
            background: #1a1a2e;
            color: #ffffff;
            border: 1px solid #16213e;
        }

        .message-loading {
            align-self: flex-start;
            background: #1a1a2e;
            color: #8892b0;
            border: 1px solid #16213e;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .loading-dots {
            display: flex;
            gap: 4px;
        }

        .loading-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #8892b0;
            animation: loading 1.4s infinite ease-in-out;
        }

        .loading-dot:nth-child(1) { animation-delay: -0.32s; }
        .loading-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loading {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #16213e;
            background: #1a1a2e;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #16213e;
            border-radius: 8px;
            background: #0f0f23;
            color: #ffffff;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #00ff88;
        }

        .chat-input::placeholder {
            color: #8892b0;
        }

        .send-button {
            padding: 12px 16px;
            background: #00ff88;
            color: #0f0f23;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .send-button:hover:not(:disabled) {
            background: #00cc6a;
        }

        .send-button:disabled {
            background: #16213e;
            color: #8892b0;
            cursor: not-allowed;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-connected {
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }

        .status-disconnected {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #0f0f23;
        }

        ::-webkit-scrollbar-thumb {
            background: #16213e;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #1e2a4a;
        }

        /* Progress Indicator Styles */
        .message-progress {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border: 1px solid #00ff8830;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
        }

        .progress-container {
            width: 100%;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-title {
            font-size: 14px;
            font-weight: 600;
            color: #00ff88;
        }

        .progress-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            font-weight: 500;
        }

        .progress-status.status-pending {
            background: #ffa50020;
            color: #ffa500;
        }

        .progress-status.status-in_progress {
            background: #00bfff20;
            color: #00bfff;
        }

        .progress-status.status-completed {
            background: #00ff8820;
            color: #00ff88;
        }

        .progress-status.status-failed {
            background: #ff444420;
            color: #ff4444;
        }

        .progress-description {
            font-size: 12px;
            color: #8892b0;
            margin-bottom: 12px;
        }

        .progress-bar-container {
            width: 100%;
            height: 6px;
            background: #0f0f23;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 12px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ff88, #00bfff);
            border-radius: 3px;
            transition: width 0.5s ease-in-out;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-steps {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .progress-step {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
            font-size: 11px;
        }

        .step-icon {
            font-size: 14px;
            width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        .step-icon.step-in_progress {
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .step-content {
            flex: 1;
        }

        .step-detail {
            font-size: 10px;
            color: #4CAF50;
            font-style: italic;
            padding: 2px 6px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 3px;
            margin-top: 2px;
            display: inline-block;
        }

        .step-name {
            font-weight: 500;
            color: #ffffff;
        }

        .step-description {
            color: #8892b0;
            font-size: 10px;
        }

        .step-progress {
            font-size: 10px;
            color: #00ff88;
            font-weight: 500;
            min-width: 35px;
            text-align: right;
        }

        .progress-step.step-pending {
            opacity: 0.6;
        }

        .progress-step.step-in_progress {
            opacity: 1;
            background: rgba(0, 191, 255, 0.1);
            border-radius: 4px;
            padding: 6px 8px;
        }

        .progress-step.step-completed {
            opacity: 0.8;
        }

        .progress-step.step-failed {
            opacity: 0.8;
            color: #ff4444;
        }

        /* System Status Dashboard Styles */
        .system-status-panel {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(15, 15, 35, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #16213e;
        }

        .status-header h3 {
            color: #00ff88;
            font-size: 18px;
            margin: 0;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: #1a1a2e;
            border: 1px solid #16213e;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .status-label {
            font-size: 11px;
            color: #8892b0;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-value {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
        }

        .status-value.status-healthy {
            color: #00ff88;
        }

        .status-value.status-warning {
            color: #ffa500;
        }

        .status-value.status-error {
            color: #ff4444;
        }

        .status-alerts {
            background: #1a1a2e;
            border: 1px solid #16213e;
            border-radius: 8px;
            padding: 15px;
        }

        .alerts-header {
            font-size: 14px;
            font-weight: 600;
            color: #00ff88;
            margin-bottom: 10px;
        }

        .alerts-list {
            max-height: 150px;
            overflow-y: auto;
        }

        .alert-item {
            padding: 8px 0;
            border-bottom: 1px solid #16213e;
            font-size: 12px;
        }

        .alert-item:last-child {
            border-bottom: none;
        }

        .alert-level-critical {
            color: #ff4444;
        }

        .alert-level-error {
            color: #ff6b6b;
        }

        .alert-level-warning {
            color: #ffa500;
        }

        .alert-level-info {
            color: #8892b0;
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">
        <div class="status-dot"></div>
        <span>Connecting...</span>
    </div>

    <div class="main-container">
        <!-- Left Scanner Panel -->
        <div class="scanner-panel">
            <div class="scanner-header">
                <div class="scanner-title">Lee Method Scanner</div>
                <div class="scanner-subtitle">Live S&P 500 Pattern Detection</div>
            </div>

            <div class="scanner-stats">
                <div class="stat-item">
                    <div class="stat-value" id="activeSignals">0</div>
                    <div class="stat-label">Active Signals</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="patternAccuracy">87%</div>
                    <div class="stat-label">Pattern Accuracy</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="scansToday">1,247</div>
                    <div class="stat-label">Scans Today</div>
                </div>
            </div>

            <div class="scanner-controls">
                <div class="control-buttons">
                    <button class="btn btn-primary" id="refreshScanBtn">🔄 Refresh</button>
                    <button class="btn btn-secondary" id="configScanBtn">⚙️ Config</button>
                    <button class="btn btn-secondary" id="viewCriteriaBtn">📋 Criteria</button>
                    <button class="btn btn-secondary" id="systemStatusBtn">📊 Status</button>
                </div>
            </div>

            <!-- System Status Dashboard -->
            <div class="system-status-panel" id="systemStatusPanel" style="display: none;">
                <div class="status-header">
                    <h3>System Status Dashboard</h3>
                    <button class="btn btn-small" id="closeStatusBtn">✕</button>
                </div>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-label">Market Hours</div>
                        <div class="status-value" id="marketHoursStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Scanner Status</div>
                        <div class="status-value" id="scannerStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">AI Systems</div>
                        <div class="status-value" id="aiSystemsStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Data Sources</div>
                        <div class="status-value" id="dataSourcesStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Response Time</div>
                        <div class="status-value" id="responseTimeStatus">--</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">Success Rate</div>
                        <div class="status-value" id="successRateStatus">--</div>
                    </div>
                </div>
                <div class="status-alerts" id="statusAlerts">
                    <div class="alerts-header">Recent Alerts</div>
                    <div class="alerts-list" id="alertsList">No recent alerts</div>
                </div>
            </div>

            <div class="scanner-content" id="scannerResults">
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Loading signals...</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Initializing scanner...</div>
                        <div class="signal-strength strength-medium">LOADING</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Chat Panel -->
        <div class="chat-panel">
            <div class="chat-header">
                <div class="chat-title">A.T.L.A.S. AI Assistant</div>
                <div class="chat-subtitle">Advanced Trading & Learning Analysis System</div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message message-bot">
                    <strong>A.T.L.A.S. AI:</strong> Welcome to the Advanced Trading & Learning Analysis System! I have access to all 25+ A.T.L.A.S. capabilities including live trading, Lee Method pattern detection, ML predictions, sentiment analysis, technical analysis, and comprehensive trading education. How can I assist you today?
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        class="chat-input" 
                        id="chatInput" 
                        placeholder="Ask A.T.L.A.S. about trading strategies, market analysis, or any trading question..."
                        rows="1"
                    ></textarea>
                    <button class="send-button" id="sendButton">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let websocket = null;
        let isConnected = false;
        let sessionId = 'web_' + Date.now();
        let activeOperations = new Map();
        let progressContainer = null;

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, initializing A.T.L.A.S. interface...');

            // Force clear any stuck messages immediately
            setTimeout(() => {
                clearStuckProgressMessages();
                console.log('✅ Interface cleanup completed');
            }, 100);

            initializeInterface();
            initializeWebSocket();
            startPeriodicCleanup(); // Start periodic cleanup to prevent stuck states
        });

        // Add a global function to manually clear stuck messages (for debugging)
        window.forceCleanInterface = function() {
            console.log('🧹 Force cleaning interface...');
            clearStuckProgressMessages();

            // Also clear any loading states
            const sendButton = document.getElementById('send-button');
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.textContent = 'Send';
            }

            // Clear input if needed
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.disabled = false;
            }

            console.log('✅ Interface force cleaned');
        };

        // WebSocket initialization for real-time updates
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const port = window.location.port || (protocol === 'wss:' ? '443' : '80');
            const wsUrl = `${protocol}//${window.location.hostname}:${port}/ws/${sessionId}`;

            console.log('🔗 Attempting WebSocket connection to:', wsUrl);

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('🔗 WebSocket connected for real-time updates');
                    isConnected = true;
                    updateConnectionStatus(true);
                };

                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket disconnected');
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Attempt to reconnect after 3 seconds
                    setTimeout(initializeWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Show user-friendly error message
                    const chatMessages = document.getElementById('chatMessages');
                    if (chatMessages) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'message system-message';
                        errorDiv.innerHTML = `
                            <div style="color: #ff6b6b; padding: 10px; border: 1px solid #ff6b6b; border-radius: 5px; margin: 5px 0;">
                                ⚠️ Real-time updates disconnected. Trying to reconnect...
                            </div>
                        `;
                        chatMessages.appendChild(errorDiv);
                        scrollToBottom();
                    }
                };

            } catch (error) {
                console.error('❌ Failed to initialize WebSocket:', error);
            }
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'connection_established':
                    console.log('✅ Real-time connection established');
                    break;

                case 'progress_update':
                    updateOperationProgress(data.operation);
                    break;

                case 'operations_list':
                    displayOperationsList(data.operations);
                    break;

                default:
                    console.log('📨 Received WebSocket message:', data);
            }
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            // Add connection status indicator if it doesn't exist
            let statusIndicator = document.getElementById('connectionStatus');
            if (!statusIndicator) {
                statusIndicator = document.createElement('div');
                statusIndicator.id = 'connectionStatus';
                statusIndicator.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    padding: 5px 10px;
                    border-radius: 15px;
                    font-size: 12px;
                    font-weight: 500;
                    z-index: 1000;
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(statusIndicator);
            }

            if (connected) {
                statusIndicator.textContent = '🟢 Live Updates';
                statusIndicator.style.background = '#00ff8820';
                statusIndicator.style.color = '#00ff88';
                statusIndicator.style.border = '1px solid #00ff88';
            } else {
                statusIndicator.textContent = '🔴 Reconnecting...';
                statusIndicator.style.background = '#ff444420';
                statusIndicator.style.color = '#ff4444';
                statusIndicator.style.border = '1px solid #ff4444';
            }
        }

        function initializeInterface() {
            console.log('🚀 Initializing A.T.L.A.S. Interface...');

            // Clear any stuck progress messages from previous sessions
            clearStuckProgressMessages();

            // Set up event listeners
            setupEventListeners();

            // Initialize WebSocket connection
            initializeWebSocket();

            // Load initial data
            loadInitialData();

            // Set up auto-refresh
            setupAutoRefresh();
        }

        function clearStuckProgressMessages() {
            // Remove any stuck progress messages that might be left from previous sessions
            const progressMessages = document.querySelectorAll('.message-progress');
            progressMessages.forEach(msg => {
                console.log('🧹 Clearing stuck progress message:', msg.id);
                msg.remove();
            });

            // Also clear any messages with "Initializing A.T.L.A.S." text
            const allMessages = document.querySelectorAll('.message');
            allMessages.forEach(msg => {
                if (msg.textContent && (
                    msg.textContent.includes('Initializing A.T.L.A.S.') ||
                    msg.textContent.includes('Preparing to process your request') ||
                    msg.textContent.includes('Processing Your Request')
                )) {
                    console.log('🧹 Clearing stuck initialization message');
                    msg.remove();
                }
            });

            // Clear any progress containers that might be stuck
            const progressContainers = document.querySelectorAll('.progress-container');
            progressContainers.forEach(container => {
                if (container.style.display !== 'none') {
                    console.log('🧹 Clearing stuck progress container');
                    container.remove();
                }
            });
        }

        // Periodic cleanup to prevent stuck states
        function startPeriodicCleanup() {
            setInterval(() => {
                // Check for messages older than 30 seconds that might be stuck
                const progressMessages = document.querySelectorAll('.message-progress');
                const now = Date.now();

                progressMessages.forEach(msg => {
                    const timestamp = msg.dataset.timestamp;
                    if (timestamp && (now - parseInt(timestamp)) > 30000) {
                        console.log('🧹 Removing old progress message:', msg.id);
                        msg.remove();
                    }
                });
            }, 10000); // Check every 10 seconds
        }

        function setupEventListeners() {
            // Chat input handling
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            sendButton.addEventListener('click', sendMessage);

            // Scanner controls
            document.getElementById('refreshScanBtn').addEventListener('click', refreshScanner);
            document.getElementById('configScanBtn').addEventListener('click', showScannerConfig);
            document.getElementById('viewCriteriaBtn').addEventListener('click', showLeeMethodCriteria);
            document.getElementById('systemStatusBtn').addEventListener('click', showSystemStatus);
            document.getElementById('closeStatusBtn').addEventListener('click', hideSystemStatus);
        }

        function initializeWebSocket() {
            console.log('🔌 Initializing WebSocket connection...');

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/scanner`;

            try {
                websocket = new WebSocket(wsUrl);

                websocket.onopen = function(event) {
                    console.log('✅ WebSocket connected');
                    isConnected = true;
                    updateConnectionStatus(true);
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('❌ Error parsing WebSocket message:', error);
                    }
                };

                websocket.onclose = function(event) {
                    console.log('🔌 WebSocket disconnected');
                    isConnected = false;
                    updateConnectionStatus(false);

                    // Attempt to reconnect after 3 seconds
                    setTimeout(initializeWebSocket, 3000);
                };

                websocket.onerror = function(error) {
                    console.error('❌ WebSocket error:', error);
                    isConnected = false;
                    updateConnectionStatus(false);
                };

            } catch (error) {
                console.error('❌ Failed to initialize WebSocket:', error);
                updateConnectionStatus(false);
            }
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');

            if (connected) {
                statusElement.className = 'connection-status status-connected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Connected</span>';
            } else {
                statusElement.className = 'connection-status status-disconnected';
                statusElement.innerHTML = '<div class="status-dot"></div><span>Disconnected</span>';
            }
        }

        function handleWebSocketMessage(data) {
            console.log('📨 WebSocket message received:', data);

            if (data.type === 'scanner_update') {
                // Handle individual scanner result from WebSocket
                if (data.data && data.data.pattern_found) {
                    // Convert single result to array format for display
                    const signal = {
                        symbol: data.data.symbol,
                        price: data.data.price,
                        confidence: data.data.confidence,
                        pattern_type: 'Lee Method',
                        timestamp: new Date().toISOString()
                    };

                    // Add to existing signals or create new array
                    const existingSignals = getCurrentSignals();
                    const updatedSignals = [signal, ...existingSignals.slice(0, 19)]; // Keep latest 20

                    updateScannerDisplay(updatedSignals);

                    // Update stats with new signal count
                    const stats = {
                        active_signals: updatedSignals.length,
                        pattern_accuracy: 87,
                        scans_today: 1247
                    };
                    updateScannerStats(stats);
                }
            } else if (data.type === 'ping') {
                // Send pong response
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({ type: 'pong' }));
                }
            }
        }

        async function loadInitialData() {
            console.log('📊 Loading initial data...');

            // Test API connectivity
            await testApiConnectivity();

            // Load Lee Method signals
            await loadLeeMethodSignals();

            // Load scanner statistics
            await loadScannerStats();
        }

        async function testApiConnectivity() {
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();

                if (data.status === 'healthy') {
                    console.log('✅ API connectivity verified');
                    updateConnectionStatus(true);
                } else {
                    console.warn('⚠️ API health check failed');
                }
            } catch (error) {
                console.error('❌ API connectivity test failed:', error);
                updateConnectionStatus(false);
            }
        }

        async function loadLeeMethodSignals() {
            try {
                console.log('📈 Loading Lee Method signals...');

                const response = await fetch('/api/v1/lee_method/signals');
                const data = await response.json();

                if (data.success && data.signals) {
                    updateScannerDisplay(data.signals);
                    console.log(`✅ Loaded ${data.signals.length} Lee Method signals`);
                } else {
                    console.warn('⚠️ No Lee Method signals available');
                    showNoSignalsMessage();
                }
            } catch (error) {
                console.error('❌ Error loading Lee Method signals:', error);
                showErrorMessage('Failed to load scanner signals');
            }
        }

        async function loadScannerStats() {
            try {
                const response = await fetch('/api/v1/lee_method/stats');
                const data = await response.json();

                if (data.success) {
                    updateScannerStats(data.stats);
                }
            } catch (error) {
                console.error('❌ Error loading scanner stats:', error);
            }
        }

        function updateScannerDisplay(signals) {
            const scannerResults = document.getElementById('scannerResults');

            if (!signals || signals.length === 0) {
                showNoSignalsMessage();
                return;
            }

            let html = '';
            signals.forEach(signal => {
                const strengthClass = getStrengthClass(signal.confidence);
                const strengthText = getStrengthText(signal.confidence);

                html += `
                    <div class="signal-item" onclick="showSignalDetails('${signal.symbol}')">
                        <div class="signal-header">
                            <div class="signal-symbol">${signal.symbol}</div>
                            <div class="signal-price">$${signal.price ? signal.price.toFixed(2) : '--'}</div>
                        </div>
                        <div class="signal-details">
                            <div class="signal-confidence">Confidence: ${(signal.confidence * 100).toFixed(1)}%</div>
                            <div class="signal-strength ${strengthClass}">${strengthText}</div>
                        </div>
                    </div>
                `;
            });

            scannerResults.innerHTML = html;

            // Update active signals count
            document.getElementById('activeSignals').textContent = signals.length;
        }

        function updateScannerStats(stats) {
            if (stats) {
                if (stats.active_signals !== undefined) {
                    document.getElementById('activeSignals').textContent = stats.active_signals;
                }
                if (stats.pattern_accuracy !== undefined) {
                    document.getElementById('patternAccuracy').textContent = `${(stats.pattern_accuracy * 100).toFixed(0)}%`;
                }
                if (stats.scans_today !== undefined) {
                    document.getElementById('scansToday').textContent = stats.scans_today.toLocaleString();
                }
            }
        }

        function getStrengthClass(confidence) {
            if (confidence >= 0.8) return 'strength-high';
            if (confidence >= 0.6) return 'strength-medium';
            return 'strength-low';
        }

        function getStrengthText(confidence) {
            if (confidence >= 0.8) return 'HIGH';
            if (confidence >= 0.6) return 'MEDIUM';
            return 'LOW';
        }

        function getCurrentSignals() {
            // Extract current signals from the scanner display
            const scannerResults = document.getElementById('scannerResults');
            const signalItems = scannerResults.querySelectorAll('.signal-item');

            const signals = [];
            signalItems.forEach(item => {
                const symbol = item.querySelector('.signal-symbol')?.textContent;
                const priceText = item.querySelector('.signal-price')?.textContent;
                const confidenceText = item.querySelector('.signal-confidence')?.textContent;

                if (symbol && symbol !== 'Loading signals...') {
                    const price = parseFloat(priceText?.replace('$', '') || '0');
                    const confidence = parseFloat(confidenceText?.match(/(\d+\.?\d*)%/)?.[1] || '0') / 100;

                    signals.push({
                        symbol,
                        price,
                        confidence,
                        pattern_type: 'Lee Method',
                        timestamp: new Date().toISOString()
                    });
                }
            });

            return signals;
        }

        function showNoSignalsMessage() {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">No active signals</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">Scanner is monitoring S&P 500...</div>
                        <div class="signal-strength strength-medium">SCANNING</div>
                    </div>
                </div>
            `;

            // Update active signals count to 0
            document.getElementById('activeSignals').textContent = '0';
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Scanner Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;

            // Update active signals count to 0
            document.getElementById('activeSignals').textContent = '0';
        }

        function showErrorMessage(message) {
            const scannerResults = document.getElementById('scannerResults');
            scannerResults.innerHTML = `
                <div class="signal-item">
                    <div class="signal-header">
                        <div class="signal-symbol">Connection Error</div>
                        <div class="signal-price">--</div>
                    </div>
                    <div class="signal-details">
                        <div class="signal-confidence">${message}</div>
                        <div class="signal-strength strength-low">ERROR</div>
                    </div>
                </div>
            `;
        }

        async function showSignalDetails(symbol) {
            try {
                console.log(`📊 Loading details for ${symbol}...`);

                const response = await fetch(`/api/v1/market_data/${symbol}`);
                const data = await response.json();

                if (data.success) {
                    // Add message to chat showing signal details
                    const message = `📈 **${symbol} Signal Analysis**\n\n` +
                        `**Current Price:** $${data.price}\n` +
                        `**Pattern:** Lee Method 5-Point TTM Squeeze\n` +
                        `**Confidence:** ${(data.confidence * 100).toFixed(1)}%\n` +
                        `**Entry Signal:** ${data.signal_type}\n\n` +
                        `**Technical Indicators:**\n` +
                        `• EMA 5/8 Trend: ${data.ema_trend || 'Calculating...'}\n` +
                        `• MACD Histogram: ${data.histogram_status || 'Analyzing...'}\n` +
                        `• TTM Squeeze: ${data.squeeze_status || 'Monitoring...'}\n\n` +
                        `Click refresh to get the latest analysis from A.T.L.A.S. AI.`;

                    addBotMessage(message);
                } else {
                    addBotMessage(`❌ Unable to load detailed analysis for ${symbol}. Please try again.`);
                }
            } catch (error) {
                console.error('❌ Error loading signal details:', error);
                addBotMessage(`❌ Error loading analysis for ${symbol}. Connection issue detected.`);
            }
        }

        // Chat functionality
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = chatInput.value.trim();

            if (!message) return;

            // Add user message to chat
            addUserMessage(message);

            // Clear input and disable send button
            chatInput.value = '';
            chatInput.style.height = 'auto';
            sendButton.disabled = true;

            // Show enhanced loading message with progress tracking
            const loadingId = addProgressMessage("Initializing A.T.L.A.S. AI...", "Preparing to process your request");

            try {
                console.log('💬 Sending message to A.T.L.A.S. AI...');

                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        context: 'web_interface'
                    })
                });

                const data = await response.json();

                // Remove loading message
                removeLoadingMessage(loadingId);

                if (response.ok && data.response) {
                    addBotMessage(data.response);
                    console.log('✅ A.T.L.A.S. AI response received');
                } else {
                    addBotMessage('❌ Sorry, I encountered an error processing your request. Please try again.');
                    console.error('❌ Chat API error:', data);
                }

            } catch (error) {
                console.error('❌ Error sending message:', error);
                removeLoadingMessage(loadingId);
                addBotMessage('❌ Connection error. Please check your connection and try again.');
            } finally {
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-user';
            messageDiv.textContent = message;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addBotMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message message-bot';

            // Convert markdown-style formatting to HTML
            const formattedMessage = message
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\n/g, '<br>');

            messageDiv.innerHTML = formattedMessage;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addLoadingMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const loadingId = 'loading_' + Date.now();

            messageDiv.id = loadingId;
            messageDiv.className = 'message message-loading';
            messageDiv.innerHTML = `
                A.T.L.A.S. AI is thinking...
                <div class="loading-dots">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            return loadingId;
        }

        function removeLoadingMessage(loadingId) {
            const loadingMessage = document.getElementById(loadingId);
            if (loadingMessage) {
                loadingMessage.style.transition = 'opacity 0.3s ease';
                loadingMessage.style.opacity = '0';
                setTimeout(() => {
                    if (loadingMessage.parentNode) {
                        loadingMessage.remove();
                    }
                }, 300);
            }
        }

        // Function to manually clear all progress messages (for debugging)
        function clearAllProgressMessages() {
            const progressMessages = document.querySelectorAll('.message-progress');
            progressMessages.forEach(msg => {
                console.log('🧹 Manually clearing progress message:', msg.id);
                msg.remove();
            });
            console.log(`✅ Cleared ${progressMessages.length} progress messages`);
        }

        // Make clearAllProgressMessages available globally for debugging
        window.clearAllProgressMessages = clearAllProgressMessages;

        // Enhanced progress tracking functions
        function addProgressMessage(title, description) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const progressId = 'progress_' + Date.now();

            messageDiv.id = progressId;
            messageDiv.className = 'message message-progress';
            messageDiv.dataset.timestamp = Date.now().toString(); // Add timestamp for cleanup
            messageDiv.innerHTML = `
                <div class="progress-container">
                    <div class="progress-header">
                        <div class="progress-title">${title}</div>
                        <div class="progress-status status-in_progress">Starting...</div>
                    </div>
                    <div class="progress-description">${description}</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                    <div class="progress-steps">
                        <div class="progress-step step-pending">
                            <div class="step-icon">⏳</div>
                            <div class="step-content">
                                <div class="step-name">Initializing...</div>
                                <div class="step-description">Preparing to process your request</div>
                            </div>
                            <div class="step-progress">0%</div>
                        </div>
                    </div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();

            return progressId;
        }

        function updateOperationProgress(operation) {
            // Find existing progress message or create new one
            let progressElement = document.querySelector(`[data-operation-id="${operation.operation_id}"]`);

            if (!progressElement) {
                // Create new progress message
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message message-progress';
                messageDiv.setAttribute('data-operation-id', operation.operation_id);

                messageDiv.innerHTML = `
                    <div class="progress-container">
                        <div class="progress-header">
                            <div class="progress-title">${operation.title}</div>
                            <div class="progress-status">${getStatusText(operation.status)}</div>
                        </div>
                        <div class="progress-description">${operation.description}</div>
                        <div class="progress-bar-container">
                            <div class="progress-bar" style="width: ${operation.overall_progress * 100}%"></div>
                        </div>
                        <div class="progress-steps"></div>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);
                progressElement = messageDiv;
            }

            // Update progress bar
            const progressBar = progressElement.querySelector('.progress-bar');
            const progressStatus = progressElement.querySelector('.progress-status');
            const progressSteps = progressElement.querySelector('.progress-steps');

            if (progressBar) {
                progressBar.style.width = `${operation.overall_progress * 100}%`;
            }

            if (progressStatus) {
                progressStatus.textContent = getStatusText(operation.status);
                progressStatus.className = `progress-status status-${operation.status}`;
            }

            // Update steps with enhanced progress details
            if (progressSteps && operation.steps) {
                progressSteps.innerHTML = operation.steps.map(step => {
                    // Show detailed progress information for market scanning
                    let progressDetail = '';
                    if (step.progress_detail) {
                        progressDetail = `<div class="step-detail">${step.progress_detail}</div>`;
                    }

                    // Add data source indicators for market scanning steps
                    let dataSourceIcon = '';
                    if (step.name.includes('Fed')) {
                        dataSourceIcon = '🏛️';
                    } else if (step.name.includes('Reuters')) {
                        dataSourceIcon = '📰';
                    } else if (step.name.includes('Bloomberg')) {
                        dataSourceIcon = '📊';
                    } else if (step.name.includes('Intent')) {
                        dataSourceIcon = '🧠';
                    } else if (step.name.includes('Engine')) {
                        dataSourceIcon = '⚙️';
                    } else if (step.name.includes('Market Data')) {
                        dataSourceIcon = '📈';
                    }

                    return `
                        <div class="progress-step step-${step.status}">
                            <div class="step-icon">${dataSourceIcon || getStepIcon(step.status)}</div>
                            <div class="step-content">
                                <div class="step-name">${step.name}</div>
                                <div class="step-description">${step.description}</div>
                                ${progressDetail}
                            </div>
                            <div class="step-progress">${Math.round(step.progress * 100)}%</div>
                        </div>
                    `;
                }).join('');
            }

            // Auto-remove completed operations after 3 seconds
            if (operation.status === 'completed') {
                setTimeout(() => {
                    if (progressElement && progressElement.parentNode) {
                        progressElement.style.transition = 'opacity 0.5s ease';
                        progressElement.style.opacity = '0';
                        setTimeout(() => {
                            if (progressElement.parentNode) {
                                progressElement.remove();
                            }
                        }, 500);
                    }
                }, 3000);
            }

            scrollToBottom();
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': 'Pending...',
                'in_progress': 'Processing...',
                'completed': 'Completed ✅',
                'failed': 'Failed ❌',
                'cancelled': 'Cancelled'
            };
            return statusMap[status] || status;
        }

        function getStepIcon(status) {
            const iconMap = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏹️'
            };
            return iconMap[status] || '⏳';
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Scanner control functions
        async function refreshScanner() {
            console.log('🔄 Refreshing scanner...');

            const refreshBtn = document.getElementById('refreshScanBtn');
            const originalText = refreshBtn.innerHTML;

            refreshBtn.innerHTML = '⏳ Refreshing...';
            refreshBtn.disabled = true;

            try {
                await loadLeeMethodSignals();
                await loadScannerStats();
                console.log('✅ Scanner refreshed successfully');
            } catch (error) {
                console.error('❌ Error refreshing scanner:', error);
            } finally {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }
        }

        function showScannerConfig() {
            addBotMessage(`⚙️ **Scanner Configuration**\n\n` +
                `**Current Settings:**\n` +
                `• Pattern: Lee Method 5-Point TTM Squeeze\n` +
                `• Market: S&P 500 (350+ symbols)\n` +
                `• Scan Interval: Real-time\n` +
                `• Confidence Threshold: 60%\n\n` +
                `**Pattern Criteria:**\n` +
                `1. 3+ declining MACD histogram bars\n` +
                `2. Histogram rebound (less negative)\n` +
                `3. EMA 5/8 uptrend confirmation\n` +
                `4. Optional TTM Squeeze filter\n` +
                `5. Target first less-negative bar\n\n` +
                `To modify settings, ask me: "Change scanner configuration"`);
        }

        function showLeeMethodCriteria() {
            addBotMessage(`📋 **Lee Method Pattern Criteria**\n\n` +
                `The Lee Method uses a sophisticated 5-point TTM Squeeze pattern detection algorithm:\n\n` +
                `**Point 1: Histogram Decline Pattern**\n` +
                `• Requires 3+ consecutive declining MACD histogram bars\n` +
                `• Each bar must be more negative than the previous\n` +
                `• Indicates building downward momentum\n\n` +
                `**Point 2: Histogram Rebound Signal**\n` +
                `• First bar that is less negative (rebounds)\n` +
                `• Signals potential momentum shift\n` +
                `• This is the primary entry trigger\n\n` +
                `**Point 3: EMA 5/8 Trend Confirmation**\n` +
                `• EMA 5 must be above EMA 8 (uptrend)\n` +
                `• Confirms bullish underlying trend\n` +
                `• Filters out false signals\n\n` +
                `**Point 4: TTM Squeeze State (Optional)**\n` +
                `• Bollinger Bands inside Keltner Channels\n` +
                `• Indicates low volatility compression\n` +
                `• Enhances signal reliability\n\n` +
                `**Point 5: Entry Timing**\n` +
                `• Target the first less-negative histogram bar\n` +
                `• Optimal entry for trend reversal\n` +
                `• Risk management with stop-loss below recent low\n\n` +
                `This pattern has shown **87% accuracy** in backtesting on S&P 500 stocks.`);
        }

        function setupAutoRefresh() {
            // Refresh scanner data every 30 seconds
            setInterval(async () => {
                if (isConnected) {
                    try {
                        await loadLeeMethodSignals();
                        await loadScannerStats();
                    } catch (error) {
                        console.error('❌ Auto-refresh error:', error);
                    }
                }
            }, 30000);

            // Test API connectivity every 60 seconds
            setInterval(async () => {
                await testApiConnectivity();
            }, 60000);
        }

        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function formatPrice(price) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(price);
        }

        // Error handling
        window.addEventListener('error', function(event) {
            console.error('❌ Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('❌ Unhandled promise rejection:', event.reason);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
        });

        // System Status Dashboard Functions
        async function showSystemStatus() {
            console.log('📊 Loading system status dashboard...');

            const statusPanel = document.getElementById('systemStatusPanel');
            statusPanel.style.display = 'block';

            // Load system health data
            try {
                const response = await fetch('/api/v1/system/health');
                const healthData = await response.json();

                // Update status indicators
                updateStatusIndicator('marketHoursStatus',
                    healthData.market_hours || 'Unknown',
                    healthData.market_hours ? 'healthy' : 'warning');

                updateStatusIndicator('scannerStatus',
                    healthData.scanner_status?.status || 'Unknown',
                    healthData.scanner_status?.status === 'running' ? 'healthy' : 'warning');

                updateStatusIndicator('aiSystemsStatus',
                    healthData.orchestrator_status?.status || 'Unknown',
                    healthData.orchestrator_status?.status === 'running' ? 'healthy' : 'error');

                updateStatusIndicator('dataSourcesStatus', 'Active', 'healthy');

                const convHealth = healthData.conversation_monitoring || {};
                updateStatusIndicator('responseTimeStatus',
                    `${convHealth.average_response_time || 0}s`,
                    convHealth.average_response_time < 10 ? 'healthy' : 'warning');

                updateStatusIndicator('successRateStatus',
                    `${convHealth.success_rate || 0}%`,
                    convHealth.success_rate > 90 ? 'healthy' : 'warning');

                // Load recent alerts
                await loadRecentAlerts();

            } catch (error) {
                console.error('Failed to load system status:', error);
                updateStatusIndicator('scannerStatus', 'Error', 'error');
            }
        }

        function hideSystemStatus() {
            const statusPanel = document.getElementById('systemStatusPanel');
            statusPanel.style.display = 'none';
        }

        function updateStatusIndicator(elementId, value, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
                element.className = `status-value status-${status}`;
            }
        }

        async function loadRecentAlerts() {
            try {
                const response = await fetch('/api/v1/monitoring/alerts?hours=1');
                const alertsData = await response.json();

                const alertsList = document.getElementById('alertsList');

                if (alertsData.alerts && alertsData.alerts.length > 0) {
                    alertsList.innerHTML = alertsData.alerts.slice(0, 5).map(alert => `
                        <div class="alert-item alert-level-${alert.alert_level}">
                            <strong>${alert.alert_level.toUpperCase()}:</strong> ${alert.description}
                            <div style="font-size: 10px; color: #8892b0; margin-top: 2px;">
                                ${new Date(alert.timestamp).toLocaleTimeString()}
                            </div>
                        </div>
                    `).join('');
                } else {
                    alertsList.innerHTML = '<div style="color: #8892b0; font-style: italic;">No recent alerts</div>';
                }

            } catch (error) {
                console.error('Failed to load alerts:', error);
                document.getElementById('alertsList').innerHTML = '<div style="color: #ff4444;">Failed to load alerts</div>';
            }
        }

        console.log('🚀 A.T.L.A.S. Interface JavaScript loaded successfully');
    </script>
</body>
</html>
